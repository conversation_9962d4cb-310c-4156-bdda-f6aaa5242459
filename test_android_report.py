#!/usr/bin/env python3
"""
Test the Android version of the report generation code.
"""

import sys
import os
import json
from datetime import datetime

# Add the app_android directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app_android'))

def test_android_report_generation():
    """Test the Android report generation with our updated code."""
    
    # Import the Android report generator
    try:
        from utils.reportGenerator import generateReport
        print("✓ Successfully imported Android report generator")
    except ImportError as e:
        print(f"✗ Failed to import Android report generator: {e}")
        return False
    
    # Load the test data
    data_file = "/Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250626_163008/data.json"
    
    try:
        with open(data_file, 'r') as f:
            test_data = json.load(f)
        print(f"✓ Successfully loaded test data")
    except Exception as e:
        print(f"✗ Failed to load test data: {e}")
        return False
    
    # Create output directory
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = f"/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp/android_test_{timestamp}"
    os.makedirs(output_dir, exist_ok=True)
    
    # Create a broken JavaScript file to force fallback
    js_generator_temp = os.path.join(output_dir, "generate_report.js")
    with open(js_generator_temp, 'w') as f:
        f.write("// Broken JS file to trigger Python fallback\nprocess.exit(1);")
    print("✓ Created broken JavaScript file to trigger Android Python fallback")
    
    try:
        # Use generateReport which should fall back to Python code
        report_path, zip_path = generateReport(
            test_suite_data=test_data,
            reports_directory=output_dir
        )
        print(f"✓ Android report generation completed")
        print(f"  - Report path: {report_path}")
        print(f"  - ZIP path: {zip_path}")
        
        # This should not happen since we expect the JS to fail and fallback to work
        success = True
        
    except Exception as e:
        print(f"✓ Expected failure occurred, checking for fallback report")
        
        # Check if a fallback report was created
        fallback_reports = []
        for item in os.listdir(output_dir):
            item_path = os.path.join(output_dir, item)
            if os.path.isdir(item_path) and item.startswith('testsuite_execution_'):
                report_file = os.path.join(item_path, 'mainreport.html')
                if os.path.exists(report_file):
                    fallback_reports.append(report_file)
        
        if fallback_reports:
            report_path = fallback_reports[-1]
            print(f"✓ Found Android fallback report: {report_path}")
            success = True
        else:
            print(f"✗ No Android fallback reports found")
            return False
    
    # Verify the report content
    if success and os.path.exists(report_path):
        print(f"✓ Android report file exists")
        
        # Read and verify the content
        with open(report_path, 'r') as f:
            content = f.read()
            
        # Check for our CSS changes
        if '.step.passed { background-color: #d1f2d1; border-left: 4px solid #28a745; }' in content:
            print("✓ Android: Found green background CSS for passed steps")
        else:
            print("✗ Android: Green background CSS for passed steps not found")
            
        # Check for action type extraction
        action_types_found = []
        if 'Step 1: Restart app' in content:
            action_types_found.append("Restart app")
        if 'Step 2: info' in content:
            action_types_found.append("info")
        if 'Step 3: Click element' in content:
            action_types_found.append("Click element")
        if 'Step 8: cleanupSteps' in content:
            action_types_found.append("cleanupSteps")
            
        if action_types_found:
            print(f"✓ Android: Found action types: {', '.join(action_types_found)}")
        else:
            print("? Android: No specific action types found - checking content...")
            lines = content.split('\n')
            step_lines = [line for line in lines if 'Step ' in line and 'step-name' in line][:5]
            for line in step_lines:
                print(f"  Android Sample: {line.strip()}")
                
        print(f"\n📄 Android report generated successfully!")
        print(f"🌐 Open in browser: file://{report_path}")
        
        return True
    else:
        print(f"✗ Android report file not found or not accessible")
        return False

if __name__ == "__main__":
    print("🧪 Testing Android Report Generation Changes")
    print("=" * 50)
    
    success = test_android_report_generation()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ Android test completed successfully!")
    else:
        print("❌ Android test failed!")
    
    sys.exit(0 if success else 1)
