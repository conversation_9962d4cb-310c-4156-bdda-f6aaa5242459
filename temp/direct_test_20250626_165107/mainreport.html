<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Suite Report - 20250626_165107</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
        .header { background-color: #f5f5f5; padding: 20px; margin-bottom: 20px; }
        .content { padding: 20px; }
        .suite-summary { padding: 15px; margin-bottom: 20px; border-radius: 5px; }
        .test-case { margin-bottom: 30px; padding: 15px; border-radius: 5px; }
        .step { margin-bottom: 15px; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
        .step-header { display: flex; justify-content: space-between; margin-bottom: 10px; }
        .step-name { font-weight: bold; }
        .step-timestamp { color: #666; }
        .passed { background-color: #d4edda; border-color: #c3e6cb; }
        .failed { background-color: #f8d7da; border-color: #f5c6cb; }
        .step.passed { background-color: #d1f2d1; border-left: 4px solid #28a745; }
        .step.failed { background-color: #f8d7da; border-left: 4px solid #dc3545; }
        .error-details { margin-top: 10px; padding: 10px; background-color: #f8f9fa; border-radius: 5px; }
        .error-details pre { margin: 0; overflow-x: auto; }
    </style>
</head>
<body>
    <header class="header">
        <h1>Test Suite Report</h1>
        <div class="status-summary">
            <span>Generated at: 2025-06-26 16:51:07</span>
        </div>
    </header>
    <div class="content">
        <h2>UI Execution 26/06/2025, 16:31:00</h2>
        <div class="suite-summary failed">
            <h2>Test Suite: UI Execution 26/06/2025, 16:31:00</h2>
            <p>Status: failed</p>
            <p>Passed: 1</p>
            <p>Failed: 1</p>
            <p>Skipped: 0</p>
        </div>
        <div class="test-case failed">
            <h3>Test Case #1: health2
                            
                            
                        
                        
                            
                                 Retry
                            
                            
                                 Stop
                            
                            
                                 Remove
                            
                            8 actions</h3>
            <p>Status: failed</p>
            <p>Duration: 0ms</p>
            <div class="steps">
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-name">Step 1: Restart app</span>
                        <span class="step-timestamp">Time: Unknown</span>
                    </div>
                    <p>Status: passed</p>
                    <p>Duration: 2373ms</p>
                    
                </div>
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-name">Step 2: info action</span>
                        <span class="step-timestamp">Time: Unknown</span>
                    </div>
                    <p>Status: passed</p>
                    <p>Duration: 17ms</p>
                    
                </div>
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-name">Step 3: Click element</span>
                        <span class="step-timestamp">Time: Unknown</span>
                    </div>
                    <p>Status: passed</p>
                    <p>Duration: 1375ms</p>
                    
                </div>
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-name">Step 4: Click element</span>
                        <span class="step-timestamp">Time: Unknown</span>
                    </div>
                    <p>Status: failed</p>
                    <p>Duration: 1644ms</p>
                    
                </div>
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-name">Step 5: Wait for 1 ms</span>
                        <span class="step-timestamp">Time: Unknown</span>
                    </div>
                    <p>Status: unknown</p>
                    <p>Duration: 1013ms</p>
                    
                </div>
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-name">Step 6: If exists</span>
                        <span class="step-timestamp">Time: Unknown</span>
                    </div>
                    <p>Status: unknown</p>
                    <p>Duration: 3635ms</p>
                    
                </div>
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-name">Step 7: Launch app</span>
                        <span class="step-timestamp">Time: Unknown</span>
                    </div>
                    <p>Status: unknown</p>
                    <p>Duration: 1212ms</p>
                    
                </div>
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-name">Step 8: cleanupSteps action</span>
                        <span class="step-timestamp">Time: Unknown</span>
                    </div>
                    <p>Status: passed</p>
                    <p>Duration: 3609ms</p>
                    
                </div>
            </div>
        </div>
        <div class="test-case passed">
            <h3>Test Case #2: apple health (Copy)
                            
                            
                        
                        
                            
                                 Retry
                            
                            
                                 Stop
                            
                            
                                 Remove
                            
                            4 actions</h3>
            <p>Status: passed</p>
            <p>Duration: 0ms</p>
            <div class="steps">
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-name">Step 1: Launch app</span>
                        <span class="step-timestamp">Time: Unknown</span>
                    </div>
                    <p>Status: passed</p>
                    <p>Duration: 1168ms</p>
                    
                </div>
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-name">Step 2: Tap on element with xpath</span>
                        <span class="step-timestamp">Time: Unknown</span>
                    </div>
                    <p>Status: passed</p>
                    <p>Duration: 896ms</p>
                    
                </div>
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-name">Step 3: Click element</span>
                        <span class="step-timestamp">Time: Unknown</span>
                    </div>
                    <p>Status: passed</p>
                    <p>Duration: 1572ms</p>
                    
                </div>
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-name">Step 4: Terminate app</span>
                        <span class="step-timestamp">Time: Unknown</span>
                    </div>
                    <p>Status: passed</p>
                    <p>Duration: 1052ms</p>
                    
                </div>
            </div>
        </div>
    </div>
</body>
</html>