#!/usr/bin/env python3
"""
Test script to verify report generation changes.
This script will regenerate a report using existing test data to verify:
1. Action types show correctly (e.g., 'info' instead of 'action')
2. Step rows have green background for passed steps
"""

import sys
import os
import json
from datetime import datetime

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def test_report_generation():
    """Test the report generation with our updated code."""
    
    # Import the report generator
    try:
        from utils.reportGenerator import generateReport, regenerate_report_from_data_json
        print("✓ Successfully imported report generator")
    except ImportError as e:
        print(f"✗ Failed to import report generator: {e}")
        return False
    
    # Load the test data
    data_file = "/Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250626_163008/data.json"
    
    try:
        with open(data_file, 'r') as f:
            test_data = json.load(f)
        print(f"✓ Successfully loaded test data from {data_file}")
        print(f"  - Test suite: {test_data.get('name', 'Unknown')}")
        print(f"  - Test cases: {len(test_data.get('testCases', []))}")
        print(f"  - Status: {test_data.get('status', 'Unknown')}")
    except Exception as e:
        print(f"✗ Failed to load test data: {e}")
        return False
    
    # Create a test output directory
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = f"/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp/test_report_{timestamp}"
    
    try:
        os.makedirs(output_dir, exist_ok=True)
        print(f"✓ Created output directory: {output_dir}")
    except Exception as e:
        print(f"✗ Failed to create output directory: {e}")
        return False
    
    # Use the existing report directory and regenerate the report
    existing_report_dir = "/Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250626_163008"

    # Copy the JavaScript report generator to temp directory but make it fail to trigger Python fallback
    js_generator_source = "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/generate_report.js"
    temp_reports_dir = "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp"
    js_generator_temp = os.path.join(temp_reports_dir, "generate_report.js")

    # Create a broken JavaScript file that will cause the fallback to trigger
    if os.path.exists(js_generator_source):
        with open(js_generator_temp, 'w') as f:
            f.write("// Broken JS file to trigger Python fallback\nprocess.exit(1);")
        print("✓ Created broken JavaScript file to trigger Python fallback")

    try:
        # Use generateReport instead, which has Python fallback code
        report_path, zip_path = generateReport(
            test_suite_data=test_data,
            reports_directory="/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp"
        )
        print(f"✓ Successfully generated report using Python fallback")
        print(f"  - Report path: {report_path}")
        print(f"  - ZIP path: {zip_path}")

        # Check if the report file exists
        if os.path.exists(report_path):
            print(f"✓ Report file exists and is accessible")

            # Read a portion of the report to verify our changes
            with open(report_path, 'r') as f:
                content = f.read()

            # Check for our CSS changes
            if '.step.passed { background-color: #d1f2d1; border-left: 4px solid #28a745; }' in content:
                print("✓ Found green background CSS for passed steps")
            else:
                print("✗ Green background CSS for passed steps not found")

            # Check for action type extraction
            if 'info' in content and 'Step 2: info' in content:
                print("✓ Found action type extraction (info instead of full action name)")
            else:
                print("? Action type extraction may need verification")

            # Check for specific action types in the content
            action_types_found = []
            if 'Step 1: Restart app' in content:
                action_types_found.append("Restart app")
            if 'Step 2: info' in content:
                action_types_found.append("info")
            if 'Step 3: Click element' in content:
                action_types_found.append("Click element")
            if 'Step 8: cleanupSteps' in content:
                action_types_found.append("cleanupSteps")

            if action_types_found:
                print(f"✓ Found action types: {', '.join(action_types_found)}")
            else:
                print("? No specific action types found - checking content...")
                # Show a sample of the content for debugging
                lines = content.split('\n')
                step_lines = [line for line in lines if 'Step ' in line and 'step-name' in line][:5]
                for line in step_lines:
                    print(f"  Sample: {line.strip()}")

            print(f"\n📄 Report regenerated successfully!")
            print(f"🌐 Open in browser: file://{report_path}")

            return True
        else:
            print(f"✗ Report file was not created or path is invalid: {report_path}")
            return False

    except Exception as e:
        print(f"✗ Failed to generate report: {e}")
        import traceback
        traceback.print_exc()

        # Check if a fallback report was created anyway
        fallback_reports = []
        temp_dir = "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp"
        for item in os.listdir(temp_dir):
            item_path = os.path.join(temp_dir, item)
            if os.path.isdir(item_path) and item.startswith('testsuite_execution_'):
                report_file = os.path.join(item_path, 'mainreport.html')
                if os.path.exists(report_file):
                    fallback_reports.append(report_file)

        if fallback_reports:
            print(f"✓ Found fallback report(s): {fallback_reports}")
            # Test the most recent one
            report_path = fallback_reports[-1]
            print(f"✓ Testing fallback report: {report_path}")

            # Continue with the verification
            with open(report_path, 'r') as f:
                content = f.read()

            # Check for our CSS changes
            if '.step.passed { background-color: #d1f2d1; border-left: 4px solid #28a745; }' in content:
                print("✓ Found green background CSS for passed steps")
            else:
                print("✗ Green background CSS for passed steps not found")

            # Check for action type extraction
            action_types_found = []
            if 'Step 1: Restart app' in content:
                action_types_found.append("Restart app")
            if 'Step 2: info' in content:
                action_types_found.append("info")
            if 'Step 3: Click element' in content:
                action_types_found.append("Click element")
            if 'Step 8: cleanupSteps' in content:
                action_types_found.append("cleanupSteps")

            if action_types_found:
                print(f"✓ Found action types: {', '.join(action_types_found)}")
            else:
                print("? No specific action types found - checking content...")
                # Show a sample of the content for debugging
                lines = content.split('\n')
                step_lines = [line for line in lines if 'Step ' in line and 'step-name' in line][:5]
                for line in step_lines:
                    print(f"  Sample: {line.strip()}")

            print(f"\n📄 Fallback report found!")
            print(f"🌐 Open in browser: file://{report_path}")
            return True
        else:
            print("✗ No fallback reports found")
            return False
    finally:
        # Clean up the temporary JavaScript file
        if os.path.exists(js_generator_temp):
            os.remove(js_generator_temp)
            print("✓ Cleaned up temporary JavaScript file")

if __name__ == "__main__":
    print("🧪 Testing Report Generation Changes")
    print("=" * 50)
    
    success = test_report_generation()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ Test completed successfully!")
    else:
        print("❌ Test failed!")
    
    sys.exit(0 if success else 1)
