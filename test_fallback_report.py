#!/usr/bin/env python3
"""
Direct test of the Python fallback report generation code.
This script directly tests the HTML generation part to verify our changes.
"""

import sys
import os
import json
from datetime import datetime

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def test_fallback_html_generation():
    """Test the fallback HTML generation directly."""
    
    # Load the test data
    data_file = "/Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250626_163008/data.json"
    
    try:
        with open(data_file, 'r') as f:
            test_data = json.load(f)
        print(f"✓ Successfully loaded test data")
    except Exception as e:
        print(f"✗ Failed to load test data: {e}")
        return False
    
    # Create output directory
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = f"/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp/direct_test_{timestamp}"
    os.makedirs(output_dir, exist_ok=True)
    
    # Create the HTML content directly using our updated code
    html_content = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Suite Report - {timestamp}</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 0; padding: 20px; }}
        .header {{ background-color: #f5f5f5; padding: 20px; margin-bottom: 20px; }}
        .content {{ padding: 20px; }}
        .suite-summary {{ padding: 15px; margin-bottom: 20px; border-radius: 5px; }}
        .test-case {{ margin-bottom: 30px; padding: 15px; border-radius: 5px; }}
        .step {{ margin-bottom: 15px; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }}
        .step-header {{ display: flex; justify-content: space-between; margin-bottom: 10px; }}
        .step-name {{ font-weight: bold; }}
        .step-timestamp {{ color: #666; }}
        .passed {{ background-color: #d4edda; border-color: #c3e6cb; }}
        .failed {{ background-color: #f8d7da; border-color: #f5c6cb; }}
        .step.passed {{ background-color: #d1f2d1; border-left: 4px solid #28a745; }}
        .step.failed {{ background-color: #f8d7da; border-left: 4px solid #dc3545; }}
        .error-details {{ margin-top: 10px; padding: 10px; background-color: #f8f9fa; border-radius: 5px; }}
        .error-details pre {{ margin: 0; overflow-x: auto; }}
    </style>
</head>
<body>
    <header class="header">
        <h1>Test Suite Report</h1>
        <div class="status-summary">
            <span>Generated at: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}</span>
        </div>
    </header>
    <div class="content">
        <h2>{test_data.get('name', 'Unknown Test Suite')}</h2>"""

    # Add overall test suite status
    suite_status = test_data.get('status', 'unknown')
    suite_status_class = 'passed' if suite_status == 'passed' else 'failed'
    html_content += f"""
        <div class="suite-summary {suite_status_class}">
            <h2>Test Suite: {test_data.get('name', 'Unknown Test Suite')}</h2>
            <p>Status: {suite_status}</p>
            <p>Passed: {test_data.get('passed', 0)}</p>
            <p>Failed: {test_data.get('failed', 0)}</p>
            <p>Skipped: {test_data.get('skipped', 0)}</p>
        </div>"""

    # Add test cases and steps
    for test_idx, test_case in enumerate(test_data.get('testCases', [])):
        test_status_class = 'passed' if test_case.get('status') == 'passed' else 'failed'
        html_content += f"""
        <div class="test-case {test_status_class}">
            <h3>Test Case #{test_idx+1}: {test_case.get('name', 'Unnamed Test')}</h3>
            <p>Status: {test_case.get('status', 'unknown')}</p>
            <p>Duration: {test_case.get('duration', '0ms')}</p>
            <div class="steps">"""

        for step_idx, step in enumerate(test_case.get('steps', [])):
            status_class = 'passed' if step.get('status') == 'passed' else 'failed'
            
            # Extract action type from step name - THIS IS OUR KEY CHANGE
            raw_name = step.get('name', '')
            action_type = raw_name.split(':')[0].strip() if ':' in raw_name else raw_name or 'Unknown'
            
            html_content += f"""
                <div class="step {status_class}">
                    <div class="step-header">
                        <span class="step-name">Step {step_idx+1}: {action_type}</span>
                        <span class="step-timestamp">Time: {step.get('timestamp', 'Unknown')}</span>
                    </div>
                    <p>Status: {step.get('status', 'unknown')}</p>
                    <p>Duration: {step.get('duration', '0ms')}</p>
                    {f"<div class='error-details'><h4>Error:</h4><pre>{step.get('error', '')}</pre></div>" if step.get('error') else ""}
                </div>"""

        html_content += """
            </div>
        </div>"""

    html_content += """
    </div>
</body>
</html>"""

    # Write the HTML file
    report_path = os.path.join(output_dir, "mainreport.html")
    try:
        with open(report_path, 'w') as f:
            f.write(html_content)
        print(f"✓ Successfully created HTML report: {report_path}")
    except Exception as e:
        print(f"✗ Failed to write HTML file: {e}")
        return False
    
    # Verify our changes
    print("\n🔍 Verifying changes:")
    
    # Check for green background CSS
    if '.step.passed { background-color: #d1f2d1; border-left: 4px solid #28a745; }' in html_content:
        print("✓ Green background CSS for passed steps is present")
    else:
        print("✗ Green background CSS for passed steps not found")
    
    # Check for action type extraction
    action_types_found = []
    if 'Step 1: Restart app' in html_content:
        action_types_found.append("Restart app")
    if 'Step 2: info' in html_content:
        action_types_found.append("info")
    if 'Step 3: Click element' in html_content:
        action_types_found.append("Click element")
    if 'Step 8: cleanupSteps' in html_content:
        action_types_found.append("cleanupSteps")
        
    if action_types_found:
        print(f"✓ Action types extracted correctly: {', '.join(action_types_found)}")
    else:
        print("? Checking what action types were actually generated...")
        lines = html_content.split('\n')
        step_lines = [line for line in lines if 'Step ' in line and 'step-name' in line][:8]
        for line in step_lines:
            print(f"  Generated: {line.strip()}")
    
    print(f"\n📄 Direct test report created successfully!")
    print(f"🌐 Open in browser: file://{report_path}")
    
    return True

if __name__ == "__main__":
    print("🧪 Testing Fallback HTML Generation Directly")
    print("=" * 50)
    
    success = test_fallback_html_generation()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ Direct test completed successfully!")
    else:
        print("❌ Direct test failed!")
    
    sys.exit(0 if success else 1)
